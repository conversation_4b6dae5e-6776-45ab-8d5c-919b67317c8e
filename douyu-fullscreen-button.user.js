// ==UserScript==
// @name         斗鱼全屏按钮
// @name:en      Douyu Fullscreen Button
// @namespace    http://tampermonkey.net/
// @version      1.6.0
// @description  为斗鱼直播间添加智能全屏按钮，支持透明度倒计时效果，双击播放器全屏，自动选择最高画质，主动监控防止页面脚本强制退出全屏并自动重新全屏（仅一次），提供优雅的全屏体验（仅支持PC端）
// @description:en Add smart fullscreen button to Douyu live rooms with opacity countdown effect, double-click fullscreen, auto highest quality selection, and prevent page scripts from forcing fullscreen exit with auto re-enter (PC only)
// <AUTHOR>
// @match        https://www.douyu.com/*
// @match        https://douyu.com/*
// @icon         data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iMTIiIGZpbGw9IiMzMzMiLz4KPHN2ZyB4PSIxNiIgeT0iMTYiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIj4KPHN0cm9rZSBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Im04IDNhMSAxIDAgMCAwLTEgMXY0YTEgMSAwIDAgMCAxIDFoNGExIDEgMCAwIDAgMS0xVjRhMSAxIDAgMCAwLTEtMXoiLz4KPHN0cm9rZSBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Im0xNSAzaDRhMSAxIDAgMCAxIDEgMXY0Ii8+CjxwYXRoIGQ9Im0yMSAxNXY0YTEgMSAwIDAgMS0xIDFoLTQiLz4KPHN0cm9rZSBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Im05IDIxSDVhMSAxIDAgMCAxLTEtMXYtNCIvPgo8cGF0aCBkPSJtMyA5VjVhMSAxIDAgMCAxIDEtMWg0Ii8+Cjwvc3Ryb2tlPgo8L3N2Zz4KPC9zdmc+
// @license      MIT
// @grant        none
// @run-at       document-end
// @noframes
// ==/UserScript==

/**
 * 斗鱼全屏按钮脚本（PC端专用）
 *
 * 功能说明：
 * - 智能检测斗鱼直播间页面，仅在直播间显示全屏按钮
 * - 按钮定位在播放器右下角内部，不遮挡重要内容
 * - 5秒透明度倒计时，提供优雅的视觉反馈
 * - 支持点击立即全屏，或自动消失
 * - 支持双击播放器窗口快速全屏
 * - 自动选择最高画质，提供最佳观看体验
 * - 防止页面脚本强制退出全屏，自动重新全屏（仅触发一次）
 * - 现代化UI设计，包含悬停效果和流畅动画
 *
 * 技术特点：
 * - 使用 #js-player-video 容器进行精确定位
 * - 透明度渐变倒计时（1.0 → 0.8 → 0.6 → 0.4 → 0.2 → 0）
 * - 双击播放器窗口快速全屏功能
 * - 智能画质检测和自动选择最高画质
 * - 简化的全屏状态监听机制，专门防护页面脚本干扰
 * - 智能检测意外退出全屏并自动重新全屏
 * - 高性能动画和事件处理
 * - 完整的错误处理和调试日志
 *
 * 兼容性：支持PC端所有现代浏览器和斗鱼网页版
 * 注意：此脚本仅适用于PC端，不支持移动端
 *
 * @version 1.6.0
 * <AUTHOR>
 * @license MIT
 */

(function() {
    'use strict';

    // 检测是否在直播间页面
    const isInLiveRoom = () => {
        const url = window.location.href;
        // 匹配斗鱼直播间URL格式：www.douyu.com/数字 或 douyu.com/数字
        const liveRoomPattern = /douyu\.com\/(\d+)(?:\?|$|\/)/;
        return liveRoomPattern.test(url);
    };

    // 自动切换最高画质功能
    const switchToHighestQuality = () => {
        try
        {
            // 查找画质选择器
            const qualitySelector = document.querySelector('.c5-6a3710[value="画质 "]');
            if (!qualitySelector)
            {
                console.debug("来自**斗鱼全屏按钮**: 未找到画质选择器");
                return;
            }

            // 查找画质列表
            const qualityList = qualitySelector.nextElementSibling;
            if (!qualityList || qualityList.tagName !== 'UL')
            {
                console.debug("来自**斗鱼全屏按钮**: 未找到画质列表");
                return;
            }

            // 获取第一个画质选项（通常是最高画质）
            const firstQualityOption = qualityList.querySelector('li:first-child');
            if (!firstQualityOption)
            {
                console.debug("来自**斗鱼全屏按钮**: 未找到画质选项");
                return;
            }

            // 检查是否已经是最高画质
            if (firstQualityOption.classList.contains('selected-3a8039'))
            {
                console.debug("来自**斗鱼全屏按钮**: 已经是最高画质");
                return;
            }

            // 点击切换到最高画质
            firstQualityOption.click();
            console.debug("来自**斗鱼全屏按钮**: 已切换到最高画质");
        } catch (error)
        {
            console.error('来自**斗鱼全屏按钮**: 切换画质失败:', error);
        }
    };

    // 自动重新全屏状态管理
    let autoReenterFullscreen = false;
    let fullscreenExitTimeout = null;
    let hasTriggeredAutoReenter = false;
    let isCurrentlyInFullscreen = false;
    let fullscreenCheckInterval = null;

    // 全屏功能
    const switchFullMode = () => {
        if (!document.fullscreenElement)
        {
            const videoContainer = document.querySelector("#js-player-video-case");
            if (videoContainer)
            {
                videoContainer.requestFullscreen().catch(err => {
                    console.error('全屏请求失败:', err);
                });
            }
        } else
        {
            document.exitFullscreen();
        }
    };

    // 启用自动重新全屏功能
    const enableAutoReenterFullscreen = () => {
        // 如果已经触发过自动重新全屏，不再启用
        if (hasTriggeredAutoReenter)
        {
            console.debug("来自**斗鱼全屏按钮**: 自动重新全屏功能已使用过，不再启用");
            return;
        }

        autoReenterFullscreen = true;
        isCurrentlyInFullscreen = true; // 标记当前应该处于全屏状态
        console.debug("来自**斗鱼全屏按钮**: 已启用自动重新全屏功能");

        // 启动主动监控机制，防止事件监听失效
        startFullscreenMonitoring();
    };

    // 启动全屏状态主动监控
    const startFullscreenMonitoring = () => {
        // 清除之前的监控
        if (fullscreenCheckInterval)
        {
            clearInterval(fullscreenCheckInterval);
        }

        // 每100ms检查一次全屏状态
        fullscreenCheckInterval = setInterval(() => {
            if (!autoReenterFullscreen || hasTriggeredAutoReenter)
            {
                // 如果不需要监控或已经触发过，停止监控
                clearInterval(fullscreenCheckInterval);
                fullscreenCheckInterval = null;
                return;
            }

            const actuallyInFullscreen = !!document.fullscreenElement;

            // 如果应该在全屏但实际不在全屏，触发自动重新全屏
            if (isCurrentlyInFullscreen && !actuallyInFullscreen)
            {
                console.debug("来自**斗鱼全屏按钮**: 主动监控检测到意外退出全屏");
                triggerAutoReenterFullscreen();
            }
        }, 100);

        console.debug("来自**斗鱼全屏按钮**: 全屏状态主动监控已启动");
    };

    // 触发自动重新全屏
    const triggerAutoReenterFullscreen = () => {
        if (hasTriggeredAutoReenter)
        {
            return; // 已经触发过，不再触发
        }

        console.debug("来自**斗鱼全屏按钮**: 准备自动重新全屏");

        // 永久标记已触发，确保只触发一次
        hasTriggeredAutoReenter = true;
        autoReenterFullscreen = false; // 禁用自动重新全屏功能

        // 停止主动监控
        if (fullscreenCheckInterval)
        {
            clearInterval(fullscreenCheckInterval);
            fullscreenCheckInterval = null;
        }

        // 清除之前的定时器
        if (fullscreenExitTimeout)
        {
            clearTimeout(fullscreenExitTimeout);
        }

        // 延迟后自动重新全屏
        fullscreenExitTimeout = setTimeout(() => {
            const videoContainer = document.querySelector("#js-player-video-case");
            if (videoContainer && !document.fullscreenElement)
            {
                videoContainer.requestFullscreen()
                    .then(() => {
                        console.debug("来自**斗鱼全屏按钮**: 自动重新全屏成功，已阻止页面脚本强制退出全屏");
                        isCurrentlyInFullscreen = true;
                    })
                    .catch(err => {
                        console.error('来自**斗鱼全屏按钮**: 自动重新全屏失败:', err);
                        isCurrentlyInFullscreen = false;
                    });
            } else
            {
                console.debug("来自**斗鱼全屏按钮**: 自动重新全屏条件不满足");
                isCurrentlyInFullscreen = false;
            }
        }, 150); // 适当延迟确保页面脚本执行完成
    };

    // 全屏状态变化监听器 - 更新状态和辅助触发
    const handleFullscreenChange = () => {
        const currentlyInFullscreen = !!document.fullscreenElement;

        // 更新当前全屏状态
        if (currentlyInFullscreen)
        {
            isCurrentlyInFullscreen = true;
            console.debug("来自**斗鱼全屏按钮**: 已进入全屏状态");
        } else
        {
            // 如果用户主动退出全屏，更新状态
            if (!autoReenterFullscreen || hasTriggeredAutoReenter)
            {
                isCurrentlyInFullscreen = false;
                console.debug("来自**斗鱼全屏按钮**: 已退出全屏状态");
            }
            // 如果启用了自动重新全屏且尚未触发过，让主动监控处理
        }

        // 辅助触发机制 - 作为主动监控的补充
        if (autoReenterFullscreen && !hasTriggeredAutoReenter && isCurrentlyInFullscreen && !currentlyInFullscreen)
        {
            console.debug("来自**斗鱼全屏按钮**: 事件监听器检测到意外退出全屏");
            // 短暂延迟后触发，让主动监控优先处理
            setTimeout(() => {
                if (!hasTriggeredAutoReenter)
                {
                    triggerAutoReenterFullscreen();
                }
            }, 50);
        }
    };

    // 添加双击播放器全屏功能
    const addDoubleClickFullscreen = () => {
        const playerVideo = document.querySelector("#js-player-video");
        const videoElement = document.querySelector("#js-player-video video");

        if (playerVideo)
        {
            // 为播放器容器添加双击事件
            playerVideo.addEventListener('dblclick', (event) => {
                // 防止事件冒泡
                event.preventDefault();
                event.stopPropagation();

                enableAutoReenterFullscreen(); // 启用自动重新全屏功能
                switchFullMode();
                console.debug("来自**斗鱼全屏按钮**: 双击播放器触发全屏");
            });

            console.debug("来自**斗鱼全屏按钮**: 双击全屏功能已启用");
        }

        // 如果找到video元素，也为其添加双击事件（备用方案）
        if (videoElement)
        {
            videoElement.addEventListener('dblclick', (event) => {
                event.preventDefault();
                event.stopPropagation();

                enableAutoReenterFullscreen(); // 启用自动重新全屏功能
                switchFullMode();
                console.debug("来自**斗鱼全屏按钮**: 双击视频元素触发全屏");
            });
        }
    };

    // 创建全屏按钮
    const createFullscreenButton = () => {
        // 查找播放器容器
        const playerContainer = document.querySelector("#js-player-video");
        if (!playerContainer)
        {
            console.error('来自**斗鱼全屏按钮**: 未找到播放器容器 #js-player-video');
            return null;
        }

        // 设置播放器容器为相对定位
        if (getComputedStyle(playerContainer).position === 'static')
        {
            playerContainer.style.position = 'relative';
        }

        // 创建按钮容器
        const button = document.createElement('div');
        button.id = 'douyu-fullscreen-btn';
        button.style.cssText = `
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 12px;
            cursor: pointer;
            z-index: 999999;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(0.8);
            font-family: Arial, sans-serif;
            user-select: none;
        `;

        // 创建全屏图标
        const icon = document.createElement('div');
        icon.style.cssText = `
            width: 24px;
            height: 24px;
            position: relative;
            margin-bottom: 4px;
        `;

        // 创建四个角的图标
        const corners = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];
        corners.forEach(corner => {
            const cornerElement = document.createElement('div');
            cornerElement.style.cssText = `
                position: absolute;
                width: 8px;
                height: 8px;
                border: 2px solid white;
            `;

            switch (corner)
            {
                case 'top-left':
                    cornerElement.style.cssText += `
                        top: 0;
                        left: 0;
                        border-right: none;
                        border-bottom: none;
                    `;
                    break;
                case 'top-right':
                    cornerElement.style.cssText += `
                        top: 0;
                        right: 0;
                        border-left: none;
                        border-bottom: none;
                    `;
                    break;
                case 'bottom-left':
                    cornerElement.style.cssText += `
                        bottom: 0;
                        left: 0;
                        border-right: none;
                        border-top: none;
                    `;
                    break;
                case 'bottom-right':
                    cornerElement.style.cssText += `
                        bottom: 0;
                        right: 0;
                        border-left: none;
                        border-top: none;
                    `;
                    break;
            }
            icon.appendChild(cornerElement);
        });

        button.appendChild(icon);
        playerContainer.appendChild(button);

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInScale {
                from {
                    opacity: 0;
                    transform: scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }

            @keyframes fadeOut {
                from {
                    opacity: 1;
                    transform: scale(1);
                }
                to {
                    opacity: 0;
                    transform: scale(0.8);
                }
            }
        `;
        document.head.appendChild(style);

        // 淡入动画
        setTimeout(() => {
            button.style.animation = 'fadeInScale 0.3s ease-out forwards';
        }, 100);

        // 鼠标悬停效果
        button.addEventListener('mouseenter', () => {
            if (!button.classList.contains('removing'))
            {
                button.style.background = 'rgba(0, 0, 0, 0.9)';
                button.style.transform = 'scale(1.1)';
                button.style.boxShadow = '0 6px 25px rgba(0, 0, 0, 0.4)';
            }
        });

        button.addEventListener('mouseleave', () => {
            if (!button.classList.contains('removing'))
            {
                button.style.background = 'rgba(0, 0, 0, 0.6)';
                button.style.transform = 'scale(1)';
                button.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
            }
        });

        // 点击事件
        button.addEventListener('click', () => {
            enableAutoReenterFullscreen(); // 启用自动重新全屏功能
            switchFullMode();
            removeButton();
            console.debug("来自**斗鱼全屏按钮**: 用户点击按钮进入全屏");
        });

        // 透明度渐变倒计时功能
        let timeLeft = 5;
        let currentOpacity = 1.0;

        const countdownInterval = setInterval(() => {
            timeLeft--;
            currentOpacity = timeLeft * 0.2; // 每秒递减0.2透明度

            if (timeLeft > 0)
            {
                button.style.opacity = currentOpacity;
                console.debug(`来自**斗鱼全屏按钮**: 倒计时 ${timeLeft}秒，透明度: ${currentOpacity}`);
            } else
            {
                clearInterval(countdownInterval);
                removeButton();
                console.debug("来自**斗鱼全屏按钮**: 倒计时结束，按钮自动消失");
            }
        }, 1000);

        // 移除按钮函数
        const removeButton = () => {
            if (button.classList.contains('removing')) return;

            button.classList.add('removing');
            clearInterval(countdownInterval);
            button.style.animation = 'fadeOut 0.3s ease-out forwards';

            setTimeout(() => {
                if (playerContainer.contains(button))
                {
                    playerContainer.removeChild(button);
                }
            }, 300);
        };

        return { button, removeButton };
    };



    // 主函数
    const init = () => {
        // 添加基本的全屏状态变化监听器
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        console.debug("来自**斗鱼全屏按钮**: 全屏状态监听器已添加");

        // 添加双击播放器全屏功能
        addDoubleClickFullscreen();

        // 延迟执行，确保页面加载完成，且仅在直播间页面显示
        setTimeout(() => {
            if (isInLiveRoom())
            {
                createFullscreenButton();
                console.debug("来自**斗鱼全屏按钮**: 检测到直播间页面，全屏按钮已显示");

                // 自动切换到最高画质
                switchToHighestQuality();
            } else
            {
                console.debug("来自**斗鱼全屏按钮**: 非直播间页面，跳过按钮显示");
            }
        }, 2000); // 延迟2秒显示按钮
    };

    // 启动
    init();
})();
