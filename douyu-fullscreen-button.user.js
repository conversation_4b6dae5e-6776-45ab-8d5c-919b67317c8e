// ==UserScript==
// @name         斗鱼全屏按钮
// @name:en      Douyu Fullscreen Button
// @namespace    http://tampermonkey.net/
// @version      2.0.0
// @description  为斗鱼直播间添加智能全屏按钮，支持透明度倒计时效果，双击播放器全屏，自动选择最高画质，增强全屏监听机制（多重检测+MutationObserver+自适应恢复），强制全屏功能，提供优雅的全屏体验（仅支持PC端）
// @description:en Add smart fullscreen button to Douyu live rooms with opacity countdown effect, double-click fullscreen, auto highest quality selection, and prevent page scripts from forcing fullscreen exit with auto re-enter (PC only)
// <AUTHOR>
// @match        https://www.douyu.com/*
// @match        https://douyu.com/*
// @icon         data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iMTIiIGZpbGw9IiMzMzMiLz4KPHN2ZyB4PSIxNiIgeT0iMTYiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIj4KPHN0cm9rZSBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Im04IDNhMSAxIDAgMCAwLTEgMXY0YTEgMSAwIDAgMCAxIDFoNGExIDEgMCAwIDAgMS0xVjRhMSAxIDAgMCAwLTEtMXoiLz4KPHN0cm9rZSBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Im0xNSAzaDRhMSAxIDAgMCAxIDEgMXY0Ii8+CjxwYXRoIGQ9Im0yMSAxNXY0YTEgMSAwIDAgMS0xIDFoLTQiLz4KPHN0cm9rZSBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Im05IDIxSDVhMSAxIDAgMCAxLTEtMXYtNCIvPgo8cGF0aCBkPSJtMyA5VjVhMSAxIDAgMCAxIDEtMWg0Ii8+Cjwvc3Ryb2tlPgo8L3N2Zz4KPC9zdmc+
// @license      MIT
// @grant        none
// @run-at       document-end
// @noframes
// ==/UserScript==


(function() {
    'use strict';

    // 检测是否在直播间页面
    const isInLiveRoom = () => {
        const url = window.location.href;
        // 匹配斗鱼直播间URL格式：www.douyu.com/数字 或 douyu.com/数字
        const liveRoomPattern = /douyu\.com\/(\d+)(?:\?|$|\/)/;
        return liveRoomPattern.test(url);
    };

    // 自动切换最高画质功能
    const switchToHighestQuality = () => {
        try
        {
            // 查找画质选择器
            const qualitySelector = document.querySelector('.c5-6a3710[value="画质 "]');
            if (!qualitySelector)
            {
                console.debug("来自**斗鱼全屏按钮**: 未找到画质选择器");
                return;
            }

            // 查找画质列表
            const qualityList = qualitySelector.nextElementSibling;
            if (!qualityList || qualityList.tagName !== 'UL')
            {
                console.debug("来自**斗鱼全屏按钮**: 未找到画质列表");
                return;
            }

            // 获取第一个画质选项（通常是最高画质）
            const firstQualityOption = qualityList.querySelector('li:first-child');
            if (!firstQualityOption)
            {
                console.debug("来自**斗鱼全屏按钮**: 未找到画质选项");
                return;
            }

            // 检查是否已经是最高画质
            if (firstQualityOption.classList.contains('selected-3a8039'))
            {
                console.debug("来自**斗鱼全屏按钮**: 已经是最高画质");
                return;
            }

            // 点击切换到最高画质
            firstQualityOption.click();
            console.debug("来自**斗鱼全屏按钮**: 已切换到最高画质");
        } catch (error)
        {
            console.error('来自**斗鱼全屏按钮**: 切换画质失败:', error);
        }
    };

    // 增强的全屏状态监听管理
    let fullscreenCheckInterval = null; // 持续监听定时器
    let lastFullscreenState = false; // 上一次检测到的全屏状态（标准API）
    let lastDouyuFullscreenState = false; // 上一次检测到的斗鱼全屏状态
    let fullscreenExitTimeout = null; // 自动重新全屏延迟定时器
    let mutationObserver = null; // DOM变化观察器

    // 全屏功能
    const switchFullMode = () => {
        if (!document.fullscreenElement)
        {
            const videoContainer = document.querySelector("#js-player-video-case");
            if (videoContainer)
            {
                videoContainer.requestFullscreen().catch(err => {
                    console.error('全屏请求失败:', err);
                });
            }
        } else
        {
            document.exitFullscreen();
        }
    };

    // 检测斗鱼全屏状态的多种方法
    const getDouyuFullscreenState = () => {
        // 方法1：检查斗鱼特定的全屏页面类名
        const hasFullscreenPageClass = document.querySelector('.is-fullScreenPage') !== null;

        // 方法2：检查标准浏览器全屏API
        const hasStandardFullscreen = !!document.fullscreenElement;

        // 方法3：检查播放器容器的全屏状态
        const videoContainer = document.querySelector("#js-player-video-case");
        const hasVideoContainerFullscreen = videoContainer && videoContainer.matches(':fullscreen');

        return {
            douyuFullscreen: hasFullscreenPageClass,
            standardFullscreen: hasStandardFullscreen,
            videoFullscreen: hasVideoContainerFullscreen,
            anyFullscreen: hasFullscreenPageClass || hasStandardFullscreen || hasVideoContainerFullscreen
        };
    };

    // 启动增强的全屏状态监听机制
    const startContinuousFullscreenMonitoring = () => {
        console.debug("来自**斗鱼全屏按钮**: 启动增强全屏状态监听");

        // 方法1：定时检查（作为备用）
        fullscreenCheckInterval = setInterval(() => {
            const currentState = getDouyuFullscreenState();
            const currentStandardFullscreen = currentState.standardFullscreen;
            const currentDouyuFullscreen = currentState.douyuFullscreen;

            // 检测标准全屏API的状态变化
            if (lastFullscreenState && !currentStandardFullscreen)
            {
                console.debug("来自**斗鱼全屏按钮**: 检测到标准全屏退出，立即恢复全屏");
                triggerAutoReenterFullscreen();
            }

            // 检测斗鱼全屏状态变化
            if (lastDouyuFullscreenState && !currentDouyuFullscreen)
            {
                console.debug("来自**斗鱼全屏按钮**: 检测到斗鱼全屏退出，立即恢复全屏");
                triggerAutoReenterFullscreen();
            }

            // 更新状态记录
            lastFullscreenState = currentStandardFullscreen;
            lastDouyuFullscreenState = currentDouyuFullscreen;
        }, 100); // 提高检测频率到100ms

        // 方法2：使用MutationObserver监听DOM变化
        startMutationObserver();
    };

    // 启动DOM变化监听器
    const startMutationObserver = () => {
        if (mutationObserver)
        {
            mutationObserver.disconnect();
        }

        mutationObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                // 监听class属性变化
                if (mutation.type === 'attributes' && mutation.attributeName === 'class')
                {
                    const target = mutation.target;

                    // 检查是否是全屏相关的class变化
                    if (target.classList.contains('is-fullScreenPage') ||
                        target.tagName === 'HTML' ||
                        target.tagName === 'BODY')
                    {

                        const currentState = getDouyuFullscreenState();

                        // 如果检测到退出全屏
                        if (!currentState.anyFullscreen && (lastFullscreenState || lastDouyuFullscreenState))
                        {
                            console.debug("来自**斗鱼全屏按钮**: MutationObserver检测到全屏退出，立即恢复全屏");
                            triggerAutoReenterFullscreen();
                        }
                    }
                }
            });
        });

        // 监听整个文档的class属性变化
        mutationObserver.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['class'],
            subtree: true
        });

        console.debug("来自**斗鱼全屏按钮**: MutationObserver已启动");
    };

    // 触发自动重新全屏（增强版本）
    const triggerAutoReenterFullscreen = () => {
        console.debug("来自**斗鱼全屏按钮**: 准备执行自动重新全屏");

        // 清除之前的定时器
        if (fullscreenExitTimeout)
        {
            clearTimeout(fullscreenExitTimeout);
        }

        // 延迟执行自动重新全屏，确保页面脚本执行完成
        fullscreenExitTimeout = setTimeout(() => {
            const currentState = getDouyuFullscreenState();

            // 如果当前没有任何形式的全屏，则尝试恢复
            if (!currentState.anyFullscreen)
            {
                const videoContainer = document.querySelector("#js-player-video-case");

                if (videoContainer)
                {
                    // 尝试标准全屏API
                    videoContainer.requestFullscreen()
                        .then(() => {
                            console.debug("来自**斗鱼全屏按钮**: 自动重新全屏成功（标准API）");
                            // 更新状态
                            lastFullscreenState = true;
                        })
                        .catch(err => {
                            console.error('来自**斗鱼全屏按钮**: 标准全屏API失败:', err);

                            // 如果标准API失败，尝试斗鱼的双击全屏方式
                            tryDouyuFullscreen();
                        });
                } else
                {
                    console.debug("来自**斗鱼全屏按钮**: 未找到播放器容器");
                }
            } else
            {
                console.debug("来自**斗鱼全屏按钮**: 当前已处于全屏状态，无需恢复");
            }
        }, 150); // 适当延迟确保页面脚本执行完成
    };

    // 尝试斗鱼的双击全屏方式
    const tryDouyuFullscreen = () => {
        try
        {
            // 尝试双击播放器容器触发斗鱼全屏
            const playerContainer = document.querySelector(".video-container-dbc7dc") ||
                document.querySelector("._1GyzL9trVIbYlAVmuA9KJ1") ||
                document.querySelector("#js-player-video-case");

            if (playerContainer)
            {
                const dblClickEvent = new MouseEvent("dblclick", {
                    bubbles: true,
                    cancelable: true,
                });
                playerContainer.dispatchEvent(dblClickEvent);
                console.debug("来自**斗鱼全屏按钮**: 尝试通过双击事件触发斗鱼全屏");

                // 更新状态
                setTimeout(() => {
                    const newState = getDouyuFullscreenState();
                    lastDouyuFullscreenState = newState.douyuFullscreen;
                }, 100);
            } else
            {
                console.debug("来自**斗鱼全屏按钮**: 未找到可双击的播放器容器");
            }
        } catch (error)
        {
            console.error('来自**斗鱼全屏按钮**: 双击全屏尝试失败:', error);
        }
    };

    // 简化的全屏状态变化监听器 - 仅用于日志记录
    const handleFullscreenChange = () => {
        const currentlyInFullscreen = !!document.fullscreenElement;

        if (currentlyInFullscreen)
        {
            console.debug("来自**斗鱼全屏按钮**: 已进入全屏状态");
        } else
        {
            console.debug("来自**斗鱼全屏按钮**: 已退出全屏状态");
        }
    };

    // 添加双击播放器全屏功能
    const addDoubleClickFullscreen = () => {
        const playerVideo = document.querySelector("#js-player-video");
        const videoElement = document.querySelector("#js-player-video video");

        if (playerVideo)
        {
            // 为播放器容器添加双击事件
            playerVideo.addEventListener('dblclick', (event) => {
                // 防止事件冒泡
                event.preventDefault();
                event.stopPropagation();

                switchFullMode();
                console.debug("来自**斗鱼全屏按钮**: 双击播放器触发全屏");
            });

            console.debug("来自**斗鱼全屏按钮**: 双击全屏功能已启用");
        }

        // 如果找到video元素，也为其添加双击事件（备用方案）
        if (videoElement)
        {
            videoElement.addEventListener('dblclick', (event) => {
                event.preventDefault();
                event.stopPropagation();

                switchFullMode();
                console.debug("来自**斗鱼全屏按钮**: 双击视频元素触发全屏");
            });
        }
    };

    // 创建全屏按钮
    const createFullscreenButton = () => {
        // 查找播放器容器
        const playerContainer = document.querySelector("#js-player-video");
        if (!playerContainer)
        {
            console.error('来自**斗鱼全屏按钮**: 未找到播放器容器 #js-player-video');
            return null;
        }

        // 设置播放器容器为相对定位
        if (getComputedStyle(playerContainer).position === 'static')
        {
            playerContainer.style.position = 'relative';
        }

        // 创建按钮容器
        const button = document.createElement('div');
        button.id = 'douyu-fullscreen-btn';
        button.style.cssText = `
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 12px;
            cursor: pointer;
            z-index: 999999;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(0.8);
            font-family: Arial, sans-serif;
            user-select: none;
        `;

        // 创建全屏图标
        const icon = document.createElement('div');
        icon.style.cssText = `
            width: 24px;
            height: 24px;
            position: relative;
            margin-bottom: 4px;
        `;

        // 创建四个角的图标
        const corners = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];
        corners.forEach(corner => {
            const cornerElement = document.createElement('div');
            cornerElement.style.cssText = `
                position: absolute;
                width: 8px;
                height: 8px;
                border: 2px solid white;
            `;

            switch (corner)
            {
                case 'top-left':
                    cornerElement.style.cssText += `
                        top: 0;
                        left: 0;
                        border-right: none;
                        border-bottom: none;
                    `;
                    break;
                case 'top-right':
                    cornerElement.style.cssText += `
                        top: 0;
                        right: 0;
                        border-left: none;
                        border-bottom: none;
                    `;
                    break;
                case 'bottom-left':
                    cornerElement.style.cssText += `
                        bottom: 0;
                        left: 0;
                        border-right: none;
                        border-top: none;
                    `;
                    break;
                case 'bottom-right':
                    cornerElement.style.cssText += `
                        bottom: 0;
                        right: 0;
                        border-left: none;
                        border-top: none;
                    `;
                    break;
            }
            icon.appendChild(cornerElement);
        });

        button.appendChild(icon);
        playerContainer.appendChild(button);

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInScale {
                from {
                    opacity: 0;
                    transform: scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }

            @keyframes fadeOut {
                from {
                    opacity: 1;
                    transform: scale(1);
                }
                to {
                    opacity: 0;
                    transform: scale(0.8);
                }
            }
        `;
        document.head.appendChild(style);

        // 淡入动画
        setTimeout(() => {
            button.style.animation = 'fadeInScale 0.3s ease-out forwards';
        }, 100);

        // 鼠标悬停效果
        button.addEventListener('mouseenter', () => {
            if (!button.classList.contains('removing'))
            {
                button.style.background = 'rgba(0, 0, 0, 0.9)';
                button.style.transform = 'scale(1.1)';
                button.style.boxShadow = '0 6px 25px rgba(0, 0, 0, 0.4)';
            }
        });

        button.addEventListener('mouseleave', () => {
            if (!button.classList.contains('removing'))
            {
                button.style.background = 'rgba(0, 0, 0, 0.6)';
                button.style.transform = 'scale(1)';
                button.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
            }
        });

        // 点击事件
        button.addEventListener('click', () => {
            switchFullMode();
            removeButton();
            console.debug("来自**斗鱼全屏按钮**: 用户点击按钮进入全屏");
        });

        // 透明度渐变倒计时功能
        let timeLeft = 5;
        let currentOpacity = 1.0;

        const countdownInterval = setInterval(() => {
            timeLeft--;
            currentOpacity = timeLeft * 0.2; // 每秒递减0.2透明度

            if (timeLeft > 0)
            {
                button.style.opacity = currentOpacity;
                console.debug(`来自**斗鱼全屏按钮**: 倒计时 ${timeLeft}秒，透明度: ${currentOpacity}`);
            } else
            {
                clearInterval(countdownInterval);
                removeButton();
                console.debug("来自**斗鱼全屏按钮**: 倒计时结束，按钮自动消失");
            }
        }, 1000);

        // 移除按钮函数
        const removeButton = () => {
            if (button.classList.contains('removing')) return;

            button.classList.add('removing');
            clearInterval(countdownInterval);
            button.style.animation = 'fadeOut 0.3s ease-out forwards';

            setTimeout(() => {
                if (playerContainer.contains(button))
                {
                    playerContainer.removeChild(button);
                }
            }, 300);
        };

        return { button, removeButton };
    };



    // 主函数
    const init = () => {
        // 立即启动持续的全屏状态监听机制
        startContinuousFullscreenMonitoring();

        // 添加基本的全屏状态变化监听器（用于日志记录）
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        console.debug("来自**斗鱼全屏按钮**: 全屏状态监听器已添加");

        // 添加双击播放器全屏功能
        addDoubleClickFullscreen();

        // 延迟执行，确保页面加载完成，且仅在直播间页面显示
        setTimeout(() => {
            if (isInLiveRoom())
            {
                createFullscreenButton();
                console.debug("来自**斗鱼全屏按钮**: 检测到直播间页面，全屏按钮已显示");

                // 自动切换到最高画质
                switchToHighestQuality();
            } else
            {
                console.debug("来自**斗鱼全屏按钮**: 非直播间页面，跳过按钮显示");
            }
        }, 2000); // 延迟2秒显示按钮
    };

    // 启动
    init();
})();
